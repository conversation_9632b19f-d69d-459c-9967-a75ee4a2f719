<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  url: string
  index?: number
  totalCount?: number
  label?: string
}

const props = defineProps<Props>()

const emit = defineEmits(['click', 'preview', 'download'])

/**
 * 获取实际的URL（去除|后的文件名部分）
 */
const actualUrl = computed((): string => {
  return props.url.includes('|') ? props.url.split('|')[0] : props.url
})

/**
 * 获取文件类型
 */
const fileType = computed((): string => {
  if (!props.url) return 'unknown'

  // 如果URL包含|分隔符，只使用|前面的实际URL部分
  const actualUrl = props.url.includes('|') ? props.url.split('|')[0] : props.url
  const extension = actualUrl.split('.').pop()?.toLowerCase() || ''

  // 图片类型
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image'
  }

  // 文档类型
  if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(extension)) {
    return 'document'
  }

  return 'unknown'
})

/**
 * 获取附件名称
 */
const fileName = computed((): string => {
  if (props.label) return props.label

  if (fileType.value === 'image') {
    return props.totalCount && props.totalCount > 1 ? `附件${props.index! + 1}` : '照片'
  }

  if (!props.url) return ''

  // 检查URL中是否包含 | 分隔符，如果有则使用 | 后面的文件名
  if (props.url.includes('|')) {
    const parts = props.url.split('|')
    if (parts.length > 1 && parts[1].trim()) {
      return parts[1].trim()
    }
  }

  // 从URL中提取文件名
  const parts = props.url.split('/')
  const fullName = parts[parts.length - 1]

  // 处理URL编码的文件名
  try {
    return decodeURIComponent(fullName)
  } catch (e) {
    return fullName
  }
})

/**
 * 获取对应的图标
 */
const fileIcon = computed((): string => {
  switch (fileType.value) {
    case 'image':
      return 'picture'
    case 'document':
      return 'file-icon'
    default:
      return 'file'
  }
})

/**
 * 处理预览事件
 */
const handlePreview = (event: Event) => {
  event.stopPropagation()
  emit('preview', props.url)
}

/**
 * 处理下载事件
 */
const handleDownload = (event: Event) => {
  event.stopPropagation()
  emit('download', props.url)
}

/**
 * 处理点击事件（保持向后兼容）
 */
const handleClick = () => {
  emit('click', props.url)
}
</script>

<template>
  <view class="attachment-item">
    <template v-if="fileType === 'image'">
      <image :src="actualUrl" mode="aspectFill" class="attachment-image" @tap="handlePreview" />
      <view class="attachment-label">{{ fileName }}</view>
      <view class="image-actions">
        <view class="action-btn preview-btn" @tap="handlePreview">
          <wd-icon name="eye" size="16px" color="#fff" />
          <text class="action-text">预览</text>
        </view>
        <view class="action-btn download-btn" @tap="handleDownload">
          <wd-icon name="download" size="16px" color="#fff" />
          <text class="action-text">下载</text>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="document-item">
        <wd-icon :name="fileIcon" size="40px" color="#1989fa" />
        <view class="document-name">{{ fileName }}</view>
        <view class="document-actions">
          <view class="action-btn preview-btn" @tap="handlePreview">
            <wd-icon name="eye" size="16px" color="#1989fa" />
            <text class="action-text">预览</text>
          </view>
          <view class="action-btn download-btn" @tap="handleDownload">
            <wd-icon name="download" size="16px" color="#1989fa" />
            <text class="action-text">下载</text>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.attachment-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  border-radius: 8rpx;
}

.attachment-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.attachment-label {
  position: absolute;
  right: 0;
  bottom: 40rpx;
  left: 0;
  padding: 4rpx 8rpx;
  overflow: hidden;
  font-size: 20rpx;
  color: #fff;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.5);
}

.image-actions {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  background-color: rgba(0, 0, 0, 0.7);
}

.document-item {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 16rpx;
  background-color: #f5f7fa;
}

.document-name {
  display: -webkit-box;
  margin-top: 8rpx;
  overflow: hidden;
  font-size: 22rpx;
  color: #606266;
  text-align: center;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.document-actions {
  display: flex;
  gap: 8rpx;
  margin-top: 8rpx;
}

.action-text {
  margin-left: 4rpx;
  font-size: 18rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 4rpx 8rpx;
  cursor: pointer;
  border-radius: 16rpx;
  transition: opacity 0.2s;

  &:active {
    opacity: 0.7;
  }
}

.preview-btn {
  background-color: #1989fa;

  .action-text {
    color: #fff;
  }
}

.download-btn {
  background-color: #67c23a;

  .action-text {
    color: #fff;
  }
}

// 文档类型的按钮样式调整
.document-item .preview-btn {
  background-color: #ecf5ff;

  .action-text {
    color: #1989fa;
  }
}

.document-item .download-btn {
  background-color: #f0f9ff;

  .action-text {
    color: #67c23a;
  }
}
</style>
